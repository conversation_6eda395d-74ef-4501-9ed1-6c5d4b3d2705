Starting master-s_2.0.py at 11-08-2025 12:13:06.34 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.67s/it]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.67s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00,  9.48it/s]
Starting: 100%|##########| 1/1 [00:00<00:00,  9.48it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.76s/it]
Processing: 100%|##########| 1/1 [00:10<00:00, 10.76s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:35<00:00, 35.08s/it]
Processing: 100%|##########| 1/1 [00:35<00:00, 35.08s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.77it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  5.51it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:03<00:00,  3.02s/it]
Processing: 100%|##########| 1/1 [00:03<00:00,  3.02s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 11.19it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:38<00:00, 38.65s/it]
Processing: 100%|##########| 1/1 [00:38<00:00, 38.65s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:48<00:00, 48.53s/it]
Finishing: 100%|##########| 1/1 [00:48<00:00, 48.53s/it]
SUCCESS: master-s_2.0.py completed successfully at 11-08-2025 12:15:43.07 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 11-08-2025 12:16:03.01 
