import csv
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
from prettytable import PrettyTable
import datetime

# Import the rich progress module
from rich_progress import print_status, create_progress_bar, RICH_AVAILABLE

# Function to count rows in a CSV file
def count_csv_rows(file_path):
    try:
        with open(file_path, mode='r', encoding='utf-8') as file:
            reader = csv.reader(file)
            row_count = sum(1 for row in reader) - 1  # Subtract 1 for header
        return row_count
    except Exception as e:
        print_status(f"Error counting rows in {file_path}: {str(e)}", "error")
        return 0

# Function to read Filter ID and Filter Name values from a CSV file
def read_csv(file_path):
    fid_values = []
    rb_names = []
    with open(file_path, mode='r') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header row
        for row in reader:
            fid_values.append(row[0])
            rb_names.append(row[1])
    return fid_values, rb_names

# Path to your CSV file
csv_file_path = r"C:\Users\<USER>\OneDrive\My Files\fid_mathews_rbnames.csv"

# Read the CSV file
fid_values, rb_names = read_csv(csv_file_path)

# Ensure that the length of fid_values and rb_names are the same
if len(fid_values) != len(rb_names):
    raise ValueError("The length of fid_values and rb_names must be the same.")

# Get row counts for each file
row_counts = []
print_status("Counting rows in Mathews replied bounces CSV files...", "info")

# Create a progress bar for counting rows
count_progress, update_count = create_progress_bar(len(rb_names), "Counting rows", "blue")

for i, rb_name in enumerate(rb_names):
    file_path = os.path.abspath(f"H:\\Master Bounces and Unsubs\\Replied Ext\\Prev_Rep_bounces_csv\\{rb_name}.csv")
    update_count(0, f"Counting rows in {rb_name}.csv")

    # Check if file exists
    if os.path.exists(file_path):
        count = count_csv_rows(file_path)
        row_counts.append(count)
        update_count(1, f"Counted {count} rows in {rb_name}.csv")
    else:
        print_status(f"File not found: {file_path}", "warning")
        row_counts.append(0)
        update_count(1, f"File not found: {rb_name}.csv")

# Stop the progress bar
if RICH_AVAILABLE:
    count_progress.stop()

# Display the values in a table
table = PrettyTable()
table.field_names = ["Filter ID", "Filter Name", "Row Count"]
for fid, rb_name, count in zip(fid_values, rb_names, row_counts):
    table.add_row([fid, rb_name, count])

# Print header with rich formatting
print_status("DBMS Auto Report Upload Process - Mathews Replied Bounces", "header")
print_status("Files to be uploaded:", "info")
print(table)

timestr = time.strftime("%d%m%Y")

# Selenium setup
print_status("Setting up Chrome browser...", "info")
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])

# Add logging preferences to capture browser logs
options.add_argument('log-level=3')  # Set log level to errors only

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("window-size=1920,1080")
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# Disable extensions and other features that might cause errors
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")

# Upload preferences
prefs = {
    "upload.default_directory": r"H:\Master Bounces and Unsubs\Replied Ext\Prev_Rep_bounces_csv",
    "upload.prompt_for_download": False,
    "upload.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False
}
options.add_experimental_option('prefs', prefs)

# Initialize Chrome browser
driver = webdriver.Chrome(options=options)
driver.set_window_size(1920, 1080)  # Ensure proper rendering

# Login to the website
print_status("Logging in to DBMS...", "info")
driver.get("http://swa.dbms.org.in/")
driver.find_element(By.ID, "username").send_keys("<EMAIL>")
driver.find_element(By.ID, "password").send_keys("Magnus@123")
time.sleep(3)
driver.find_element(By.ID, "btnSubmit").click()
time.sleep(3)

# Create a progress bar for the file upload process
print_status("Starting Mathews replied bounces upload process...", "info")
total_files = len(fid_values)

# Function to take screenshot when upload fails
def take_screenshot(driver, rb_name):
    try:
        # Create screenshots directory if it doesn't exist
        screenshot_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "screenshots")
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{screenshot_dir}/mathews_error_{rb_name}_{timestamp}.png"
        
        # Take screenshot and save
        driver.save_screenshot(filename)
        print_status(f"Screenshot saved to {filename}", "info")
        return filename
    except Exception as e:
        print_status(f"Failed to take screenshot: {str(e)}", "error")
        return None

# Modify the upload process to take screenshots on failure
try:
    # Create a progress bar with orange gradient for Mathews files
    progress, update_progress = create_progress_bar(total_files, "Uploading Mathews files", "orange")

    # Iterate over fid, rb_name, and row_count values
    for i, (fid, rb_name, count) in enumerate(zip(fid_values, rb_names, row_counts)):
        # Update progress description with row count
        update_progress(0, f"Uploading {rb_name} - {count} rows ({i+1}/{total_files})")

        # Navigate to the upload page
        driver.get(f"http://swa.dbms.org.in/upload_data_file.php?datafilter={fid}&datafiltername={rb_name}")
        button = driver.find_element(By.ID, 'up_file')
        time.sleep(2)

        # Define the file path
        file_path = os.path.abspath(f"H:\\Master Bounces and Unsubs\\Replied Ext\\Prev_Rep_bounces_csv\\{rb_name}.csv")

        # Check if file exists and has rows
        if not os.path.exists(file_path):
            print_status(f"File not found: {file_path}", "error")
            take_screenshot(driver, f"{rb_name}_file_not_found")
            update_progress(1, f"Skipped {rb_name} - file not found")
            continue

        if count == 0:
            print_status(f"File has no data rows: {file_path}", "warning")
            take_screenshot(driver, f"{rb_name}_no_data")
            update_progress(1, f"Skipped {rb_name} - no data rows")
            continue

        try:
            # Direct file upload using send_keys
            button.send_keys(file_path)

            # Check duplicates and submit
            driver.find_element(By.ID, "duplicates").click()
            time.sleep(3)
            driver.find_element(By.ID, "submit_key").click()
            time.sleep(10)

            # Update progress
            update_progress(1)
            print_status(f"Uploaded {rb_name} with {count} rows", "success")
        except Exception as e:
            print_status(f"Error uploading {rb_name}: {str(e)}", "error")
            screenshot_path = take_screenshot(driver, rb_name)
            print_status(f"Error details captured in screenshot: {screenshot_path}", "info")
            update_progress(1, f"Failed {rb_name} - error during upload")

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Calculate summary statistics
    uploaded_files = sum(1 for count in row_counts if count > 0)
    total_rows = sum(row_counts)

    # Print completion message with statistics
    print_status("Mathews Replied Bounces Upload Process Completed", "header")
    print_status(f"Uploaded {uploaded_files} of {total_files} files", "success")
    print_status(f"Total rows processed: {total_rows}", "success")

    # Create a summary table
    summary_table = PrettyTable()
    summary_table.field_names = ["Metric", "Value"]
    summary_table.add_row(["Total Files", total_files])
    summary_table.add_row(["Uploaded Files", uploaded_files])
    summary_table.add_row(["Skipped Files", total_files - uploaded_files])
    summary_table.add_row(["Total Rows", total_rows])
    summary_table.add_row(["Average Rows Per File", round(total_rows / uploaded_files) if uploaded_files > 0 else 0])

    print(summary_table)

except Exception as e:
    print_status(f"Error during Mathews upload process: {str(e)}", "error")

finally:
    # Close the browser
    print_status("Closing browser...", "info")
    driver.quit()
